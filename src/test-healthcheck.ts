import dotenv from "dotenv";
import { redisService } from "./services/redis";
import { HealthcheckService } from "./services/healthcheck";

dotenv.config();

async function testHealthcheck() {
  try {
    console.log("🧪 Testing healthcheck functionality...");

    // Connect to Redis
    console.log("🔗 Connecting to Redis...");
    await redisService.connect();

    // Write healthcheck
    console.log("📝 Writing healthcheck...");
    await HealthcheckService.writeInitHealthcheck();

    // Read healthcheck
    console.log("📖 Reading healthcheck...");
    const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
    console.log(`Last healthcheck: ${lastHealthcheck}`);

    // Check health status
    console.log("🔍 Checking health status...");
    const isHealthy = await HealthcheckService.isHealthy();
    console.log(`Health status: ${isHealthy ? "✅ Healthy" : "❌ Unhealthy"}`);

    // Disconnect
    await redisService.disconnect();
    console.log("✅ Test completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

testHealthcheck();
