import bot from "./bot";
import dotenv from "dotenv";
import { redisService } from "./services/redis";
import { HealthcheckService } from "./services/healthcheck";

dotenv.config();

const PORT = process.env.PORT ?? 3001;
const NODE_ENV = process.env.NODE_ENV ?? "development";
const WEBHOOK_URL = process.env.WEBHOOK_URL;

async function startBot() {
  try {
    console.log("🤖 Starting Marketplace Bot...");

    // Initialize Redis connection
    console.log("🔗 Connecting to Redis...");
    await redisService.connect();

    // Write healthcheck to Redis
    await HealthcheckService.writeInitHealthcheck();

    const botInfo = await bot.telegram.getMe();
    console.log(`✅ Bot @${botInfo.username} is ready`);
    console.log(`🔍 Bot ID: ${botInfo.id}`);

    if (NODE_ENV === "production" && WEBHOOK_URL) {
      console.log("🌐 Setting up webhook for production...");
      console.log(`🚀 Bot webhook server started on port ${PORT}`);
    } else {
      console.log("🔄 Starting bot in polling mode (development)...");

      console.log("🔄 Launching bot...");
      bot
        .launch()
        .then(() => {
          console.log("🚀 Bot started successfully in polling mode");
        })
        .catch((error: any) => {
          console.error("❌ Failed to launch bot:", error);
          if (
            error.message?.includes("409") ||
            error.message?.includes("Conflict")
          ) {
            console.log(
              "💡 Another bot instance might be running. Please stop it first."
            );
          }
        });
    }

    console.log("🔧 Setting up bot configuration...");

    try {
      await bot.telegram.setChatMenuButton({
        menuButton: {
          type: "web_app",
          text: "PREM",
          web_app: {
            url:
              process.env.WEB_APP_URL ??
              "https://4d5rqhd0-3000.euw.devtunnels.ms/",
          },
        },
      });

      console.log("✅ Menu button configured");
    } catch (error) {
      console.log("⚠️ Failed to set menu button:", error);
    }

    try {
      await bot.telegram.setMyCommands([
        { command: "start", description: "Start the bot and show main menu" },
        { command: "help", description: "Show help information" },
        { command: "health", description: "Check bot health status" },
      ]);
      console.log("✅ Commands configured");
    } catch (error) {
      console.log("⚠️ Failed to set commands:", error);
    }

    try {
      await bot.telegram.setMyDescription(
        "🛍️ Marketplace Bot - Your gateway to the PREM marketplace platform. Use the menu button or commands to get started!"
      );
      await bot.telegram.setMyShortDescription(
        "🛍️ Access the marketplace platform"
      );
      console.log("✅ Description configured");
    } catch (error) {
      console.log("⚠️ Failed to set description:", error);
    }

    console.log("🎉 Bot setup completed successfully!");
    console.log("📋 Available buttons:");
    console.log("  🛒 My Buy Orders");
    console.log("  💰 My Sell Orders");
    console.log("  🔗 Get Referral Link");
    console.log("  📞 Contact Support");
    console.log("  🌐 Open Marketplace (Web App)");
  } catch (error) {
    console.error("❌ Failed to start bot:", error);
    process.exit(1);
  }
}

process.once("SIGINT", async () => {
  console.log("🛑 Shutting down gracefully...");
  bot.stop("SIGINT");
  await redisService.disconnect();
  console.log("✅ Redis disconnected");
  process.exit(0);
});

process.once("SIGTERM", async () => {
  console.log("🛑 Shutting down gracefully...");
  bot.stop("SIGTERM");
  await redisService.disconnect();
  console.log("✅ Redis disconnected");
  process.exit(0);
});

startBot();
